//
//  DIYTagsBottomLeftComplication.swift
//  DIYTagsBottomLeftComplication
//
//  Created by Mac2024 on 2025/4/3.
//

import WidgetKit
import SwiftUI

// MARK: - 共享实用工具类
class WidgetUtilities {
    static let shared = WidgetUtilities()
    let appGroupIdentifier = "group.com.watchfaceos.flytheme"
    
    // 图片文件名
    private let topLeftImageFileName = "topLeftImageWeightName.png"
    private let bottomLeftImageFileName = "bottomLeftImageWeightName.png"
    private let BottomCenterImageFileName = "BottomCenterImageWeightName.png"
    private let bottomRightImageFileName = "bottomRightImageWeightName.png"
    private let CenterImageFileName = "CenterImageWeightName.png"
    
    private let defaultImageFileName = "selectedImage.png" // 兼容旧版
    
    private init() {}
    
    // 记录日志消息
    func logMessage(_ message: String, source: String) {
        print("📱 \(source): \(message)")
    }
    
    // 获取图片文件URL
    private func getImageFileURL(forWidget widgetType: String? = nil) -> URL? {
        guard let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            return nil
        }
        
        // 根据小组件类型返回不同的文件路径
        if let widgetType = widgetType {
            if widgetType == "topLeft" {
                return containerURL.appendingPathComponent(topLeftImageFileName)
            } else if widgetType == "bottomLeft" {
                return containerURL.appendingPathComponent(bottomLeftImageFileName)
            } else if widgetType == "bottomRight" {
                return containerURL.appendingPathComponent(bottomRightImageFileName)
            } else if widgetType == "bottomCenter" {
                return containerURL.appendingPathComponent(BottomCenterImageFileName)
            } else if widgetType == "center" {
                return containerURL.appendingPathComponent(CenterImageFileName)
            }
    
        }
        
        // 默认返回旧版文件名（兼容性）
        return containerURL.appendingPathComponent(defaultImageFileName)
    }
    
    // 从App Group加载图片
    func loadImageFromAppGroup(forBottomWidget: Bool = false) -> UIImage? {
        // 先尝试加载小组件特定图片
        let widgetType = forBottomWidget ? "bottomLeft" : nil
        let source = forBottomWidget ? "BottomWidget" : "WidgetUtils"
        
        guard let imageURL = getImageFileURL(forWidget: widgetType) else {
            logMessage("无法获取App Group路径", source: source)
            return nil
        }
        
        logMessage("检查图片文件: \(imageURL.path)", source: source)
        
        if FileManager.default.fileExists(atPath: imageURL.path) {
            logMessage("图片文件存在", source: source)
            if let imageData = try? Data(contentsOf: imageURL),
               let image = UIImage(data: imageData) {
                logMessage("成功加载图片", source: source)
                return image
            } else {
                logMessage("无法解析图片数据", source: source)
            }
        } else {
            logMessage("图片文件不存在", source: source)
            
            // 如果是底部小组件且特定图片不存在，尝试加载通用图片
            if forBottomWidget {
                logMessage("尝试加载通用图片", source: source)
                if let defaultURL = getImageFileURL(),
                   FileManager.default.fileExists(atPath: defaultURL.path),
                   let imageData = try? Data(contentsOf: defaultURL),
                   let image = UIImage(data: imageData) {
                    logMessage("成功加载通用图片", source: source)
                    return image
                }
            }
        }
        
        // 回退到内置的默认图片
        logMessage("使用默认图片", source: source)
        return UIImage(named: "01")
    }
}

struct Provider: TimelineProvider {
    //记录位置 无用
    let source = "小组件"
    
    // 日志记录函数
    private func logMessage(_ message: String) {
        WidgetUtilities.shared.logMessage(message, source: source)
    }
    
    func placeholder(in context: Context) -> SimpleEntry {
        logMessage("创建占位符")
        // 加载底部小组件专用图片
        let image = WidgetUtilities.shared.loadImageFromAppGroup(forBottomWidget: true)
        return SimpleEntry(date: Date(), image: image)
    }
    
    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        logMessage("获取快照")
        // 加载底部小组件专用图片
        let image = WidgetUtilities.shared.loadImageFromAppGroup(forBottomWidget: true)
        let entry = SimpleEntry(date: Date(), image: image)
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        logMessage("开始获取时间线")
        
        var entries: [SimpleEntry] = []
        // 加载底部小组件专用图片
        let image = WidgetUtilities.shared.loadImageFromAppGroup(forBottomWidget: true)
        
        let currentDate = Date()
        for hourOffset in 0 ..< 5 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = SimpleEntry(date: entryDate, image: image)
            entries.append(entry)
        }
        
        logMessage("创建时间线，条目数量: \(entries.count), 刷新策略: atEnd")
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let image: UIImage?
}

struct DIYTagsBottomLeftComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        if let image = entry.image {
            Image(uiImage: image)
                .resizable()
                .scaledToFit()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                .clipShape(Circle())
        } else {
            // 备用视图，当图片不可用时显示
            Circle()
                .fill(Color.blue.opacity(0.2))
            
            Text("?")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.blue)
        }
    }
}

@main
struct DIYTagsBottomLeftComplication: Widget {
    let kind: String = "DIYTagsBottomLeftComplication"
    
    // 初始化和日志记录
    init() {
        print("⌚️ 顶部左侧小组件初始化 - Kind: \(kind)")
        // 记录当前正在运行的小组件信息
        let message = "顶部左侧小组件启动: \(kind) at \(Date())"
        print("⌚️ Top Widget记录日志: \(message)")
    }
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                DIYTagsBottomLeftComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                DIYTagsBottomLeftComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("表盘市场-自定义贴纸左下")
        .description("显示用户在App中选择的图片")
        .supportedFamilies([.accessoryCircular])
    }
}

// watchOS 9.0 及以后的预览版本
struct DIYTagsBottomLeftComplicationEntryView_Previews: PreviewProvider {
    static var previews: some View {
        let previewImage = UIImage(named: "01")
        let entry = SimpleEntry(date: Date(), image: previewImage)
        let family: WidgetFamily = .accessoryCircular
        if #available(watchOS 10.0, *) {
            DIYTagsBottomLeftComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            DIYTagsBottomLeftComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
