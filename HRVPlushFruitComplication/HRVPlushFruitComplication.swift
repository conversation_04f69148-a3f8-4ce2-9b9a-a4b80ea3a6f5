//
//  HRVPlushFruitComplication.swift
//  HRVPlushFruitComplication
//
//  Created by Mac2024 on 2025/5/12.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVPlushFruitComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    // 进度条参数
    let progressWidth = 80.0
    let progressHeight: CGFloat = 14.0
    let progressTopPadding: CGFloat = -1
    let progressLeadingPadding: CGFloat = 16
    let dotYOffset: CGFloat = 0
    let dotSize: CGSize = CGSize(width: 10, height: 10)
    
    // 文字参数
    let textTopPadding: CGFloat = 1
    let textLeadingPadding: CGFloat = 18
    let textFontSize: CGFloat = 11
    
    var body: some View {
        ZStack {
            // 背景图片
            Image(HRVManager.shared.showZZEHimage(HRVValue: entry.value).imgName)
                .resizable()
                .scaledToFill()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            
            // 内容布局
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Spacer()
                
                    Image(HRVManager.shared.showZZEHimage(HRVValue: entry.value).heatImageName)
                        .resizable()
                        .frame(width: 15, height: 12)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                }.padding(.top, 25)
                    .padding(.trailing, 7)

                ZStack {
                    Spacer()
                    // 根据HRV值获取对应的进度条图像
                    let progressImages = HRVManager.shared.getProgressImages(value: entry.value)
                    Image(progressImages.progressImage)
                        .resizable()
                        .frame(width: progressWidth, height: progressHeight)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    // 计算偏移量
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                    // 使用对应的点图像
                    Image(progressImages.dotImage)
                        .resizable()
                        .frame(width: dotSize.width, height: dotSize.height)
                        .offset(x: offsetX, y: dotYOffset)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .padding(.top, progressTopPadding)
                .padding(.leading, progressLeadingPadding)
                
                // 显示进度百分比文本
                let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                let percentage = Int(normalizedValue * 100)
                
                Text("今日元气值\(percentage)%")
                    .foregroundStyle(HRVManager.shared.getZZEHHRVColor(value: entry.value))
                    .font(.system(size: textFontSize, weight: .bold, design: .rounded))
                    .padding(.top, textTopPadding)
                    .padding(.leading, textLeadingPadding)
              
            }
        }.widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVPlushFruitComplication: Widget {
    let kind: String = "HRVPlushFruitComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVPlushFruitComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVPlushFruitComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("毛绒水果-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本 这里为啥都是卡皮吧啦的
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVPlushFruitComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVPlushFruitComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
