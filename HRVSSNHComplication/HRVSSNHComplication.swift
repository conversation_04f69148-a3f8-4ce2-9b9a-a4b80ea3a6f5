//
//  HRVSSNHComplication.swift
//  HRVSSNHComplication
//
//  Created by Mac20<PERSON> on 2025/6/3.
//

import SwiftUI
import WidgetKit
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 25, steps: 88)
}

struct HRVSSNHComplicationEntryView: View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    // 进度条参数（可调整）
    private let progressWidth: CGFloat = 68.0
    private let progressHeight: CGFloat = 10.0
    private let progressOffsetX: CGFloat = 20
    private let progressOffsetY: CGFloat = 2
    private let progressBottomPadding: CGFloat = 8
    
    // 进度点参数（可调整）
    private let dotSize: CGFloat = 8
    
    // 文字参数（可调整）
    private let textOffsetX: CGFloat = 20
    private let textOffsetY: CGFloat = 4
    private let textFontSize: CGFloat = 12
    
    var body: some View {
        ZStack {
            // 背景图片 - 占满但不溢出
            Image(getBackgroundImageName(for: entry.value))
                .resizable()
                .scaledToFit()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            
            // 内容布局
            VStack(alignment: .trailing, spacing: 0) {
                Spacer()
                
                ZStack {
                    // 进度条背景
                    Image("processing")
                        .resizable()
                        .frame(width: progressWidth, height: progressHeight)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    // 计算进度点位置
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                    
                    // 进度点
                    Image("dot")
                        .resizable()
                        .frame(width: dotSize, height: dotSize)
                        .offset(x: offsetX, y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .padding(.trailing, progressOffsetX)
                .padding(.top, progressOffsetY)
                
                HStack {
                    Spacer()
                    
                    // 计算进度百分比，与进度条保持一致
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let percentage = Int(normalizedValue * 100)
                    
                    Text("心理压力 \(percentage)%")
                        .foregroundStyle(Color(hex: "FFF741"))
                        .font(.system(size: textFontSize, weight: .semibold))
                }
                .padding(.trailing, textOffsetX)
                .padding(.bottom, progressBottomPadding)
                .padding(.top, textOffsetY)
            }
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    // 根据HRV值获取背景图片名称
    private func getBackgroundImageName(for hrvValue: Int) -> String {
        switch hrvValue {
        case ...19:
            return "4"  // 压力过载
        case 20...39:
            return "3"  // 注意压力
        case 40..<70:
            return "2"  // 悠然自在
        default:
            return "1"  // 活力满满
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVSSNHComplication: Widget {
    let kind: String = "HRVSSNHComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVSSNHComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVSSNHComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("时尚女孩-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HRVSSNHComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVSSNHComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVSSNHComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
