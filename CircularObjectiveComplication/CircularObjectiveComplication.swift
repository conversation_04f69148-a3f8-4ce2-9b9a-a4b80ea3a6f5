//
//  CircularObjectiveComplication.swift
//  CircularObjectiveComplication
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/5.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        let entry = HRVComplicationModel.preview
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
    
    //    func relevances() async -> WidgetRelevances<Void> {
    //        // Generate a list containing the contexts this widget is relevant in.
    //    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct CircularObjectiveComplicationEntryView : View {
    var entry: Provider.Entry
    // 蓝湖UI图宽是46
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        GeometryReader { outerGeometry in
            let widgetWidth = outerGeometry.size.width
            let scaleW = widgetWidth / 51
            
            let smallCircleWH = 4.0
            let offsetY = (widgetWidth - smallCircleWH) / 2.0
            
            ZStack {
                /*
                 默认情况下（未旋转时），0度的位置在正右方（3点钟方向）
                 顺时针旋转
                 顺时针绘制
                 */
                Circle()
                    .trim(from: 0.1, to: 0.9)
                    .stroke(AngularGradient(gradient: Gradient(colors: [.red, .orange, .yellow, .green, .blue, .purple]), center: .center), style: StrokeStyle(lineWidth: 8, lineCap: .butt))
                    .rotationEffect(.degrees(90))
                
                /*
                 移动的圆点
                 范围：0.1 到 0.9
                 0度在正上方
                 角度：-135度 到 135度
                 */
                Circle()
                    .stroke(.black, lineWidth: 1)
                    .frame(width: smallCircleWH, height: smallCircleWH)
                    .offset(y: -offsetY)
                    .rotationEffect(.degrees(calculateOffsetY(for: entry, smallCircleWH: smallCircleWH)), anchor: .center)
                
                VStack(spacing: 0) {
                    Spacer()
                        .frame(height: 8 * scaleW)
                    
                    Text(entry.date, style: .time)
                        .font(.system(size: 3 * scaleW, weight: .semibold))
                    
                    Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).imgName)
                        .resizable()
                        .scaledToFit()
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    Text("\(entry.value)ms")
                        .font(.system(size: 6 * scaleW, weight: .semibold))
                    
                    Text(HRVManager.shared.showBGImageName(HRVValue: entry.value).coTextName)
                        .font(.system(size: 5 * scaleW, weight: .semibold))
                        .foregroundColor(Color(red: 108/255, green: 255/255, blue: 120/255, opacity: 1.0))
                    
                    Spacer()
                        .frame(height: 1 * scaleW)
                }
                //.background(.yellow)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            //.background(.red)
        }.widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    func calculateOffsetY(for entry: Provider.Entry, smallCircleWH: CGFloat) -> CGFloat {
        // 限制 HRV 值在 0 到 100 之间
        let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
        
        let ai = 180 - 360 / 10 - smallCircleWH;
        let maxHRVValue = Double(ai - (-ai))
        
        let offsetY = maxHRVValue / HRVManager.shared.maxHRVValue * hrv - maxHRVValue / 2.0
        
        return offsetY
    }
}

@main
struct CircularObjectiveComplication: Widget {
    let kind: String = "CircularObjectiveComplication"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                CircularObjectiveComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                CircularObjectiveComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("毛绒怪兽-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryCircular])
    }
}

// watchOS 9.0 及以后的预览版本
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryCircular
        if #available(watchOS 10.0, *) {
            CircularObjectiveComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            CircularObjectiveComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
