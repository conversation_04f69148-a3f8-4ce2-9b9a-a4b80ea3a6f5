//
//  HourSCYSComplication.swift
//  HourSCYSComplication
//
//  Created by Mac2024 on 2025/5/29.
//

import WidgetKit
import SwiftUI

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date())
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let entry = SimpleEntry(date: Date())
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [SimpleEntry] = []

        // 生成24小时的时间线，每10分钟更新一次
        let currentDate = Date()
        for minuteOffset in stride(from: 0, to: 24 * 60, by: 10) {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            let entry = SimpleEntry(date: entryDate)
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
}

struct HourSCYSComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode

    var body: some View {
        // 显示对应时辰的图片
        Image(getHourImageName(for: entry.date))
            .resizable()
            .scaledToFill()
            .widgetAccentable(renderingMode != .fullColor)
            .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            .widgetURL(URL(string: "watchapp://hour?shichen=\(getShiChen(for: entry.date))"))
    }
    
    // 获取时辰对应的图片名称
    private func getHourImageName(for date: Date) -> String {
        let shiChen = getShiChen(for: date)
        return "\(shiChen)"
    }
    
    // 根据当前时间获取对应的时辰编号（1-12）
    private func getShiChen(for date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        
        // 十二时辰对应关系：
        // 子时: 23-1点 (1)  丑时: 1-3点 (2)   寅时: 3-5点 (3)
        // 卯时: 5-7点 (4)   辰时: 7-9点 (5)   巳时: 9-11点 (6)
        // 午时: 11-13点 (7) 未时: 13-15点 (8) 申时: 15-17点 (9)
        // 酉时: 17-19点 (10) 戌时: 19-21点 (11) 亥时: 21-23点 (12)
        
        switch hour {
        case 23, 0:     return 1  // 子时
        case 1, 2:      return 2  // 丑时
        case 3, 4:      return 3  // 寅时
        case 5, 6:      return 4  // 卯时
        case 7, 8:      return 5  // 辰时
        case 9, 10:     return 6  // 巳时
        case 11, 12:    return 7  // 午时
        case 13, 14:    return 8  // 未时
        case 15, 16:    return 9  // 申时
        case 17, 18:    return 10 // 酉时
        case 19, 20:    return 11 // 戌时
        case 21, 22:    return 12 // 亥时
        default:        return 1  // 默认子时
        }
    }
}

@main
struct HourSCYSComplication: Widget {
    let kind: String = "HourSCYSComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HourSCYSComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HourSCYSComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("时辰换图-时辰养生圆盘")
        .description("根据时辰显示不同的传统文化图片")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HourSCYSComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = SimpleEntry(date: Date())
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HourSCYSComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HourSCYSComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
