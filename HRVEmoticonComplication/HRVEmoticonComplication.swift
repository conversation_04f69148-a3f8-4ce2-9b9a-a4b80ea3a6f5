//
//  HRVEmoticonComplication.swift
//  HRVEmoticonComplication
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/4.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        let entry = HRVComplicationModel.preview
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
    
    //    func relevances() async -> WidgetRelevances<Void> {
    //        // Generate a list containing the contexts this widget is relevant in.
    //    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVEmoticonComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        GeometryReader { outerGeometry in
            let widgetWidth = outerGeometry.size.width
            let widgetHeight = outerGeometry.size.height
            
            let progressHeight = 6.0
            let currentValueHeight = 8.0
            let rightImgWH = widgetHeight - currentValueHeight
            let leftTopImgWidth = widgetWidth - rightImgWH
            let leftTopImgHeight = 52.0
            VStack {
                HStack() {
                    ZStack(alignment: .leading) {
                        Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).textImgName)
                            .resizable()
                            .frame(width: leftTopImgWidth, height: leftTopImgHeight)
                            .scaledToFit()
                            .widgetAccentable(renderingMode != .fullColor)
                            .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                        
                        HStack() {
                            Text("压力:")
                                .font(.system(size: 11, weight: .regular))
                                .foregroundColor(Color(red: 255/255, green: 194/255, blue: 78/255, opacity: 1.0))
                            
                            let commonTextModifiers: (Text) -> Text = { text in
                                text
                                    .font(.system(size: 11, weight:.regular))
                                    .foregroundColor(.white)
                            }
                            
                            commonTextModifiers(Text("\(entry.value)ms"))
                            
                            Spacer()
                            
                            commonTextModifiers(Text(entry.date, style: .time))
                            
                            Spacer()
                        }
                        .offset(x: 0, y: 4)
                    }
                    
                    Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).imgName)
                        .resizable()
                        .frame(width: rightImgWH, height: rightImgWH)
                        .scaledToFit()
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                
                ZStack {
                    let progressWidth = widgetWidth
                    Image("progress")
                        .resizable()
                        .frame(width: progressWidth, height: progressHeight)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    // 计算偏移量
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                    Image("currentValue")
                        .resizable()
                        .frame(width: currentValueHeight, height: currentValueHeight)
                        .offset(x: offsetX, y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .frame(height: currentValueHeight)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVEmoticonComplication: Widget {
    let kind: String = "HRVEmoticonComplication"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVEmoticonComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVEmoticonComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("表情包-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVEmoticonComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVEmoticonComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
