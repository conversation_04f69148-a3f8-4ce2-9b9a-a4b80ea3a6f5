//
//  HRVJYMTComplication.swift
//  HRVJYMTComplication
//
//  Created by Mac2024 on 2025/7/15.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }

    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }

//    func relevances() async -> WidgetRelevances<Void> {
//        // Generate a list containing the contexts this widget is relevant in.
//    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVJYMTComplicationEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode

    var body: some View {
        // 根据HRV值选择对应的图片
        Image(getImageName(for: entry.value))
            .resizable()
            .scaledToFill() // 使用scaledToFill让图片填满整个区域
            .frame(maxWidth: .infinity, maxHeight: .infinity) // 设置最大宽高为无限以填满容器
            .clipped() // 剪裁超出的部分
            .widgetAccentable(renderingMode != .fullColor)
            .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }

    // 根据HRV值获取对应的图片名称
    private func getImageName(for hrvValue: Int) -> String {
        switch hrvValue {
        case ...19:
            return "1"  // 压力过载
        case 20...39:
            return "2"  // 注意压力
        default:
            return "3"  // 悠然自在 和 活力满满 都使用图片3
        }
    }

    // 格式化时间
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}

@main
struct HRVJYMTComplication: Widget {
    let kind: String = "HRVJYMTComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVJYMTComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVJYMTComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("HRV 金鱼")
        .description("显示金鱼图案的HRV小组件")
    }
}

// 预览
struct HRVJYMTComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVJYMTComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVJYMTComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}
