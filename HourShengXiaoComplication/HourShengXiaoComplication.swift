//
//  HourShengXiaoComplication.swift
//  HourShengXiaoComplication
//
//  Created by Mac2024 on 2025/5/14.
//

import WidgetKit
import SwiftUI

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        let now = Date()
        let shichen = getShichen(for: now)
        let imageName = getImageNameForShichen(shichen)
        return SimpleEntry(date: now, shichen: shichen, imageName: imageName)
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        let now = Date()
        let shichen = getShichen(for: now)
        let imageName = getImageNameForShichen(shichen)
        let entry = SimpleEntry(date: now, shichen: shichen, imageName: imageName)
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [SimpleEntry] = []

        let now = Date()
        let shichen = getShichen(for: now)
        let imageName = getImageNameForShichen(shichen)
        let currentEntry = SimpleEntry(date: now, shichen: shichen, imageName: imageName)
        entries.append(currentEntry)

        // 计算下一个时辰的起始时间
        let nextShichenDate = getNextShichenStartDate(from: now)
        let calendar = Calendar.current
        let maxRefreshCount = 50 // WidgetKit每小时最大刷新次数
        let minuteInterval = 1 // 理想步长：每分钟刷新
        let totalMinutes = Int(nextShichenDate.timeIntervalSince(now) / 60)
        let step: Int
        if totalMinutes + 1 <= maxRefreshCount {
            step = minuteInterval
        } else {
            step = max(1, (totalMinutes + 1) / maxRefreshCount)
        }
        var date = calendar.date(bySetting: .second, value: 0, of: now) ?? now
        while date < nextShichenDate {
            date = calendar.date(byAdding: .minute, value: step, to: date) ?? date
            if date >= nextShichenDate { break }
            let shichen = getShichen(for: date)
            let imageName = getImageNameForShichen(shichen)
            let entry = SimpleEntry(date: date, shichen: shichen, imageName: imageName)
            entries.append(entry)
        }
        // 确保下一个时辰起点也有Entry
        let nextShichen = getShichen(for: nextShichenDate)
        let nextImageName = getImageNameForShichen(nextShichen)
        let nextEntry = SimpleEntry(date: nextShichenDate, shichen: nextShichen, imageName: nextImageName)
        entries.append(nextEntry)

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }

    // 获取当前时辰编号（1-12）
    func getShichen(for date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        // 子时 23:00-0:59, 丑时 1:00-2:59, ...
        switch hour {
        case 23:
            return 1 // 子时
        case 0:
            return 1 // 子时
        case 1, 2:
            return 2 // 丑时
        case 3, 4:
            return 3 // 寅时
        case 5, 6:
            return 4 // 卯时
        case 7, 8:
            return 5 // 辰时
        case 9, 10:
            return 6 // 巳时
        case 11, 12:
            return 7 // 午时
        case 13, 14:
            return 8 // 未时
        case 15, 16:
            return 9 // 申时
        case 17, 18:
            return 10 // 酉时
        case 19, 20:
            return 11 // 戌时
        case 21, 22:
            return 12 // 亥时
        default:
            return 1 // 默认子时
        }
    }

    // 获取图片名（1-12）
    func getImageNameForShichen(_ shichen: Int) -> String {
        return String(shichen)
    }

    // 获取下一个时辰的起始时间
    func getNextShichenStartDate(from date: Date) -> Date {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        var nextHour: Int
        switch hour {
        case 23:
            nextHour = 1
        case 0:
            nextHour = 1
        case 1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21:
            nextHour = hour + 2
        case 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22:
            nextHour = hour + 1
        default:
            nextHour = hour + 1
        }
        var nextDate = calendar.date(bySettingHour: nextHour % 24, minute: 0, second: 0, of: date) ?? date
        if nextDate <= date {
            nextDate = calendar.date(byAdding: .hour, value: 1, to: nextDate) ?? date
        }
        return nextDate
    }

//    func relevances() async -> WidgetRelevances<Void> {
//        // Generate a list containing the contexts this widget is relevant in.
//    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let shichen: Int // 时辰编号 1-12
    let imageName: String // 图片名 1-12

    static let preview = SimpleEntry(date: Date(), shichen: 1, imageName: "1")

    // 计算属性，用于生成百分比字符串
    var percentageText: String {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let minute = calendar.component(.minute, from: date)
        let totalMinutesInDay = 24 * 60
        let currentMinutes = hour * 60 + minute
        let percentage = Int(Double(currentMinutes) / Double(totalMinutesInDay) * 100)
        return "\(percentage)%"
    }
}

struct HourShengXiaoComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode

    // 时辰对应的颜色数组
    let shichenColors: [Color] = [
        Color(hex: "F2000A"), Color(hex: "F55C00"), Color(hex: "1A3FFE"),
        Color(hex: "06A990"), Color(hex: "009EBB"), Color(hex: "00B070"),
        Color(hex: "0073C3"), Color(hex: "FF5725"), Color(hex: "FF8016"),
        Color(hex: "FF2E2E"), Color(hex: "FF8E0A"), Color(hex: "F2000A")
    ]

    // 新的位置参数：基于右下角的偏移量
    var offsetX: CGFloat = -5 // 从右下角基点水平偏移，负值向左
    var offsetY: CGFloat = -13 // 从右下角基点垂直偏移，负值向上

    var body: some View {
        ZStack { // 主容器
            // 底层图片
            Image(entry.imageName)
                .resizable()
                .scaledToFill()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)

            // 顶层百分比文本，自由定位
            Text(entry.percentageText)
                .font(.system(size: 12, weight: .bold))
                .foregroundColor(shichenColors[entry.shichen - 1])
                // 1. 将文本框扩展到最大，内容对齐到右下角
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottomTrailing)
                // 2. 从右下角基点进行偏移
                .offset(x: offsetX, y: offsetY)
                // 如需调试，可取消此行注释观察文本实际边界和位置
                // .background(Color.yellow.opacity(0.5))
        }
    }
}

@main
struct HourShengXiaoComplication: Widget {
    let kind: String = "HourShengXiaoComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HourShengXiaoComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HourShengXiaoComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("时辰换图-十二生肖时辰")
        .description("根据中国古代时辰显示不同图片")
    }
}

struct HourShengXiaoComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = SimpleEntry.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HourShengXiaoComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HourShengXiaoComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}


// Color扩展，用于从Hex字符串创建Color
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
