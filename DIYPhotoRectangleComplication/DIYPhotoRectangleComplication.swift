//
//  DIYPhotoRectangleComplication.swift
//  DIYPhotoRectangleComplication
//
//  Created by Mac2024 on 2025/4/9.
//

import WidgetKit
import SwiftUI

// MARK: - 共享实用工具类
class WidgetUtilities {
    static let shared = WidgetUtilities()
    let appGroupIdentifier = "group.com.watchfaceos.flytheme"
    
    // 图片文件名
    private let photoFangImageFileName = "photoFangImageWeightName.png"

    
    private let defaultImageFileName = "selectedImage.png" // 兼容旧版
    
    private init() {}
    
    // 记录日志消息
    func logMessage(_ message: String, source: String) {
        print("📱 \(source): \(message)")
    }
    
    // 获取图片文件URL
    private func getImageFileURL(forWidget widgetType: String? = nil) -> URL? {
        guard let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            return nil
        }
        
        // 根据小组件类型返回不同的文件路径
        if let widgetType = widgetType {
            if widgetType == "photoFang" {
                return containerURL.appendingPathComponent(photoFangImageFileName)
            }
        }
        
        // 默认返回旧版文件名（兼容性）
        return containerURL.appendingPathComponent(defaultImageFileName)
    }
    
    // 从App Group加载图片
    func loadImageFromAppGroup(forBottomWidget: Bool = false) -> UIImage? {
        // 先尝试加载小组件特定图片
        let widgetType = forBottomWidget ? "photoFang" : nil
        let source = forBottomWidget ? "BottomWidget" : "WidgetUtils"
        
        guard let imageURL = getImageFileURL(forWidget: widgetType) else {
            logMessage("无法获取App Group路径", source: source)
            return nil
        }
        
        logMessage("检查图片文件: \(imageURL.path)", source: source)
        
        if FileManager.default.fileExists(atPath: imageURL.path) {
            logMessage("图片文件存在", source: source)
            if let imageData = try? Data(contentsOf: imageURL),
               let image = UIImage(data: imageData) {
                logMessage("成功加载图片", source: source)
                return image
            } else {
                logMessage("无法解析图片数据", source: source)
            }
        } else {
            logMessage("图片文件不存在", source: source)
            
            // 如果是底部小组件且特定图片不存在，尝试加载通用图片
            if forBottomWidget {
                logMessage("尝试加载通用图片", source: source)
                if let defaultURL = getImageFileURL(),
                   FileManager.default.fileExists(atPath: defaultURL.path),
                   let imageData = try? Data(contentsOf: defaultURL),
                   let image = UIImage(data: imageData) {
                    logMessage("成功加载通用图片", source: source)
                    return image
                }
            }
        }
        
        // 回退到内置的默认图片
        logMessage("使用默认图片", source: source)
        return UIImage(named: "01")
    }
}

struct Provider: TimelineProvider {
    //记录位置 无用
    let source = "小组件"
    
    // 日志记录函数
    private func logMessage(_ message: String) {
        WidgetUtilities.shared.logMessage(message, source: source)
    }
    /*
    用途：
    提供一个占位视图的初始数据，用于在小组件首次加载或系统需要快速展示占位内容时显示。例如，用户将 Widget 添加到屏幕前的预览阶段。
    调用时机：
    添加 Widget 到屏幕前的预览。
    系统需要快速渲染占位内容时（如加载中状态）。
    实现要点：
    快速返回：需生成一个轻量的数据实例，避免耗时操作。
    静态数据：通常使用硬编码的示例数据（如假数据）填充视图。
    与真实数据解耦：即使真实数据未加载完成，占位内容也能展示基本布局。
    */
    func placeholder(in context: Context) -> SimpleEntry {
        logMessage("创建占位符")
        // 加载底部小组件专用图片
        let image = WidgetUtilities.shared.loadImageFromAppGroup(forBottomWidget: true)
        return SimpleEntry(date: Date(), image: image)
    }
    /*
    用途：
    生成一个"快照"数据，用于临时展示 Widget 的内容（如在小部件库中的预览或快速切换时）。
    调用时机：
    用户在小部件库中浏览 Widget 的预览。
    系统需要快速响应用户操作（如滑动到 Widget 的瞬间）。
    实现要点：
    轻量且快速：避免网络请求或复杂计算，必要时使用缓存或静态数据。
    异步支持：允许异步获取数据，但需在合理时间内调用 completion。
    区分上下文：通过 context.isPreview 判断是否为预览模式，决定是否返回简化数据。
    */
    
    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        logMessage("获取快照")
        // 加载底部小组件专用图片
        let image = WidgetUtilities.shared.loadImageFromAppGroup(forBottomWidget: true)
        let entry = SimpleEntry(date: Date(), image: image)
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        logMessage("开始获取时间线")
        
        var entries: [SimpleEntry] = []
        // 加载底部小组件专用图片
        let image = WidgetUtilities.shared.loadImageFromAppGroup(forBottomWidget: true)
        
        let currentDate = Date()
        for hourOffset in 0 ..< 5 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = SimpleEntry(date: entryDate, image: image)
            entries.append(entry)
        }
        
        logMessage("创建时间线，条目数量: \(entries.count), 刷新策略: atEnd")
        /*
        使用 .atEnd 在所有条目过期后重新加载。
        使用 .after(Date) 指定下次刷新时间，适合规律性更新（如每小时一次）。
        使用 .never 手动控制刷新（需结合后台任务）。
         */
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let image: UIImage?
}
struct DIYPhotoRectangleComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        if let image = entry.image {
            Image(uiImage: image)
                .resizable()
                .scaledToFill()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
//                .clipShape(Circle())
        } else {
            // 备用视图，当图片不可用时显示
            Circle()
                .fill(Color.blue.opacity(0.2))
            
            Text("?")
                .font(.system(size: 24, weight: .bold))
                .foregroundColor(.blue)
        }
    }
}

@main
struct DIYPhotoRectangleComplication: Widget {
    let kind: String = "DIYTagsCenterComplication"
    
    // 初始化和日志记录
    init() {
        print("⌚️ 顶部左侧小组件初始化 - Kind: \(kind)")
        // 记录当前正在运行的小组件信息
        let message = "顶部左侧小组件启动: \(kind) at \(Date())"
        print("⌚️ Top Widget记录日志: \(message)")
    }
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                DIYPhotoRectangleComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                DIYPhotoRectangleComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("表盘市场-自定照片矩形")
        .description("显示用户在App中选择的图片")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本
struct DIYPhotoRectangleComplication_Previews: PreviewProvider {
    static var previews: some View {
        let previewImage = UIImage(named: "01")
        let entry = SimpleEntry(date: Date(), image: previewImage)
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            DIYPhotoRectangleComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            DIYPhotoRectangleComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
