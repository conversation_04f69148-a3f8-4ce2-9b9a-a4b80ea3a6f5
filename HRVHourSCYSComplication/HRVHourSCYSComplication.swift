//
//  HRVHourSCYSComplication.swift
//  HRVHourSCYSComplication
//
//  Created by Mac2024 on 2025/5/29.
//

import WidgetKit
import SwiftUI

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }

    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        let entry = HRVComplicationModel.preview
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [HRVComplicationModel] = []

        // 生成24小时的时间线，每10分钟更新一次
        let currentDate = Date()
        for minuteOffset in stride(from: 0, to: 24 * 60, by: 10) {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            // 模拟HRV数据，实际应用中应该从HealthKit获取
            let mockHrvValue = Int.random(in: 15...35)
            let entry = HRVComplicationModel(date: entryDate, value: mockHrvValue, steps: 88)
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 23, steps: 88)
}

struct HRVHourSCYSComplicationEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    // 调试参数 - 设置为nil时使用真实时间，设置为1-12时强制使用指定时辰
    private let debugShiChen: Int? = nil  // 修改这个值来测试不同时辰图片: 1=子时, 2=丑时, ..., 12=亥时
    
    // 文字图层位置参数（可调整）
    private let textOffsetX: CGFloat = 6     // X轴偏移量
    private let textOffsetY: CGFloat = 7   // Y轴偏移量
    private let textFontSize: CGFloat = 10   // 字体大小
    
    // 进度条图层位置参数（可调整）
    private let progressOffsetX: CGFloat = 27  // X轴偏移量
    private let progressOffsetY: CGFloat = 24 // Y轴偏移量
    private let progressBarWidth: CGFloat = 94 // 进度条宽度
    private let progressBarHeight: CGFloat = 5 // 进度条高度
    private let progressDotSize: CGFloat = 10   // 进度点大小
    
    // 十二时辰对应的颜色
    private let shiChenColors: [Color] = [
        Color(hex: "B675FF"), // 子时 (1)
        Color(hex: "FF7B41"), // 丑时 (2)
        Color(hex: "37E557"), // 寅时 (3)
        Color(hex: "FF5A5A"), // 卯时 (4)
        Color(hex: "FF4FA2"), // 辰时 (5)
        Color(hex: "4FF1FF"), // 巳时 (6)
        Color(hex: "FFE641"), // 午时 (7)
        Color(hex: "FF479E"), // 未时 (8)
        Color(hex: "B675FF"), // 申时 (9)
        Color(hex: "72E85F"), // 酉时 (10)
        Color(hex: "F2FF64"), // 戌时 (11)
        Color(hex: "B675FF")  // 亥时 (12)
    ]

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景层：十二时辰图片 - 占满整个widget区域
                Image(getHourImageName(for: entry.date))
                    .resizable()
                    .scaledToFill()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .clipped()
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                
                // 文字图层：HRV值显示
                Text("HRV：\(entry.value)ms")
                    .font(.system(size: textFontSize, weight: .medium))
                    .foregroundColor(getShiChenColor(for: entry.date))
                    .shadow(color: .black.opacity(0.8), radius: 1, x: 0, y: 1)
                    .offset(x: textOffsetX, y: textOffsetY)
                
                // 进度条图层
                ZStack {
                    // 进度条背景
                    Image("bar")
                        .resizable()
                        .frame(width: progressBarWidth, height: progressBarHeight)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    // 进度点
                    Image("dot")
                        .resizable()
                        .frame(width: progressDotSize, height: progressDotSize)
                        .offset(x: calculateDotPosition(), y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .offset(x: progressOffsetX, y: progressOffsetY)
                
                // 调试信息显示（可选）
//                if let debugShiChen = debugShiChen {
//                    Text("调试时辰: \(debugShiChen)")
//                        .font(.system(size: 8))
//                        .foregroundColor(.red)
//                        .offset(x: -50, y: -25)
//                }
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .widgetURL(URL(string: "watchapp://hrv-hour?shichen=\(getShiChen(for: entry.date))&hrv=\(entry.value)"))
    }
    
    // 获取时辰对应的颜色
    private func getShiChenColor(for date: Date) -> Color {
        let shiChen = debugShiChen ?? getShiChen(for: date)  // 使用调试时辰或真实时辰
        // 时辰编号从1开始，数组索引从0开始，所以要减1
        let index = shiChen - 1
        return shiChenColors[index]
    }

    // 计算进度点位置
    private func calculateDotPosition() -> CGFloat {
        // HRV正常范围大约是15-50ms，这里简化处理
        let minHRV: CGFloat = 15
        let maxHRV: CGFloat = 50
        let currentHRV = CGFloat(entry.value)
        
        // 确保值在范围内
        let clampedHRV = max(minHRV, min(maxHRV, currentHRV))
        
        // 计算进度百分比
        let progress = (clampedHRV - minHRV) / (maxHRV - minHRV)
        
        // 计算偏移量（从左端-progressBarWidth/2到右端+progressBarWidth/2）
        let offsetX = (progress * progressBarWidth) - (progressBarWidth / 2)
        
        return offsetX
    }
    
    // 获取时辰对应的图片名称
    private func getHourImageName(for date: Date) -> String {
        let shiChen = debugShiChen ?? getShiChen(for: date)  // 使用调试时辰或真实时辰
        return "\(shiChen)"
    }
    
    // 根据当前时间获取对应的时辰编号（1-12）
    private func getShiChen(for date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        
        // 十二时辰对应关系：
        // 子时: 23-1点 (1)  丑时: 1-3点 (2)   寅时: 3-5点 (3)
        // 卯时: 5-7点 (4)   辰时: 7-9点 (5)   巳时: 9-11点 (6)
        // 午时: 11-13点 (7) 未时: 13-15点 (8) 申时: 15-17点 (9)
        // 酉时: 17-19点 (10) 戌时: 19-21点 (11) 亥时: 21-23点 (12)
        
        switch hour {
        case 23, 0:     return 1  // 子时
        case 1, 2:      return 2  // 丑时
        case 3, 4:      return 3  // 寅时
        case 5, 6:      return 4  // 卯时
        case 7, 8:      return 5  // 辰时
        case 9, 10:     return 6  // 巳时
        case 11, 12:    return 7  // 午时
        case 13, 14:    return 8  // 未时
        case 15, 16:    return 9  // 申时
        case 17, 18:    return 10 // 酉时
        case 19, 20:    return 11 // 戌时
        case 21, 22:    return 12 // 亥时
        default:        return 1  // 默认子时
        }
    }
}

@main
struct HRVHourSCYSComplication: Widget {
    let kind: String = "HRVHourSCYSComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVHourSCYSComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVHourSCYSComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("时辰换图-时辰养生HRV")
        .description("根据时辰显示不同的古风图片，同时显示HRV数据")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HRVHourSCYSComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVHourSCYSComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVHourSCYSComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
