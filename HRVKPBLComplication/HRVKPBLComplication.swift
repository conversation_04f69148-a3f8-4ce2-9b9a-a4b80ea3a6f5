//
//  HRVKPBLComplication.swift
//  HRVKPBLComplication
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/24.
//

import SwiftUI
import WidgetKit
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
    
    //    func relevances() async -> WidgetRelevances<Void> {
    //        // Generate a list containing the contexts this widget is relevant in.
    //    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVKPBLComplicationEntryView: View {
    var entry: Provider.Entry
    let progressWidth = 90.0
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        ZStack {
            // 背景图片 - 在非全色模式下应用luminanceToAlpha保留细节
            Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).imgName)
                .resizable()
                .scaledToFit()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            
            // 内容布局
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text(HRVManager.shared.showBGImageName(HRVValue: entry.value).titleName)
                        .foregroundStyle(.white)
                        .font(.system(size: 19, weight: .semibold))
                        .padding(.top, 28)
                        .padding(.leading, 16)
                    
                    Spacer()
                }
                
                HStack(spacing: 9) {
                    Text("\(entry.value)ms")
                        .foregroundStyle(.white)
                        .font(.system(size: 13, weight: .semibold))
                        .padding(.leading, 16)
                    
                    Text(entry.date, style: .time)
                        .foregroundStyle(.white)
                        .font(.system(size: 13, weight: .semibold))
                }
                
                ZStack {
                    Image("progress")
                        .resizable()
                        .frame(width: progressWidth, height: 8)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    // 计算偏移量
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                    Image("currentValue")
                        .resizable()
                        .frame(width: 16, height: 16)
                        .offset(x: offsetX, y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .padding(.leading, 10)
                .padding(.bottom, 10)
            }
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVKPBLComplication: Widget {
    let kind: String = "HRVKPBLComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVKPBLComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVKPBLComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("卡皮巴拉-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVKPBLComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVKPBLComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
