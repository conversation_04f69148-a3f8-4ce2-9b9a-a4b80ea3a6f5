//
//  HRVCartoonComplication.swift
//  HRVCartoonComplication
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/27.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        let entry = HRVComplicationModel.preview
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
    
    //    func relevances() async -> WidgetRelevances<Void> {
    //        // Generate a list containing the contexts this widget is relevant in.
    //    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVCartoonComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    let midHeight = 10.0
    let bottomHeight = 11.0
    
    var body: some View {
        GeometryReader { outerGeometry in
            VStack(spacing: 3) {
                let widgetWidth = outerGeometry.size.width
                let widgetHeight = outerGeometry.size.height
                // 计算图片可用的高度
                let availableHeight = widgetHeight - midHeight - bottomHeight
                
                Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).imgName)
                    .resizable()
                    .frame(width: widgetWidth, height: availableHeight)
                    .scaledToFit()
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    //.background(.green)
                
                ZStack {
                    let progressWidth = widgetWidth
                    Image("progress")
                        .resizable()
                        .frame(width: progressWidth)
                        .scaledToFit()
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    // 计算偏移量
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                    Image("currentValue")
                        .resizable()
                        .frame(width: midHeight, height: midHeight)
                        .offset(x: offsetX, y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .frame(height: midHeight)
                //.background(.yellow)
                
                HStack {
                    Text(entry.date, style: .time)
                        .font(.system(size: 12, weight: .semibold))
                    
                    Spacer()
                    
                    Image("hrvImg")
                        .resizable()
                        .frame(width: 14, height: bottomHeight)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    Text("HRV \(entry.value)ms")
                        .font(.system(size: 12, weight: .semibold))
                }
                .padding([.leading, .trailing], 2)
                .frame(height: bottomHeight)
                //.background(.blue)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
            //.background(.red)
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVCartoonComplication: Widget {
    let kind: String = "HRVCartoonComplication"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVCartoonComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVCartoonComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("卡通表情-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVCartoonComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVCartoonComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
