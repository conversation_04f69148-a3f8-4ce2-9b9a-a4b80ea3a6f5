//
//  HRVSCYScComplication.swift
//  HRVSCYScComplication
//
//  Created by Mac2024 on 2025/6/17.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }

    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 30, steps: 88)
}

struct HRVSCYScComplicationEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode

    // 可调整的文字层参数
    private let textOffsetX: CGFloat = 30        // 文字水平偏移量
    private let textOffsetY: CGFloat = 8      // 文字垂直偏移量
    private let textFontSize: CGFloat = 12      // 文字字体大小
    private let textFontWeight: Font.Weight = .medium  // 文字粗细
    private let textColor: Color = .white       // 文字颜色
    private let textShadowRadius: CGFloat = 1   // 文字阴影半径
    private let textShadowOffset: CGPoint = CGPoint(x: 0, y: 1) // 阴影偏移

    // 可调整的进度条参数
    private let progressOffsetX: CGFloat = 0    // 进度条水平偏移量
    private let progressOffsetY: CGFloat = 25   // 进度条垂直偏移量（贴最下面）
    private let progressDotHeight: CGFloat = 16 // 进度点高度

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 第一层：十二时辰背景图 - 保持宽高比，可切圆角不切边
                Image(getHourImageName(for: entry.date))
                    .resizable()
                    .scaledToFill()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .clipped()
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)

                // 第二层：文字层 - HRV数值和测量时间
                Text("\(entry.value)ms · \(formatTime(entry.date))")
                    .font(.system(size: textFontSize, weight: textFontWeight))
                    .foregroundColor(textColor)
                    .shadow(color: .black.opacity(0.8), radius: textShadowRadius, x: textShadowOffset.x, y: textShadowOffset.y)
                    .offset(x: textOffsetX, y: textOffsetY)

                // 第三层：进度条和进度点
                ZStack {
                    // 进度条背景 - 使用组件宽度
                    Image("processing")
                        .resizable()
                        .scaledToFit()
                        .frame(width: geometry.size.width)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)

                    // 进度点 - 保持原始宽高比，稍微掩盖进度条
                    Image("dot")
                        .resizable()
                        .scaledToFit()
                        .frame(height: progressDotHeight)
                        .offset(x: calculateDotPosition(geometry: geometry), y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .offset(x: progressOffsetX, y: progressOffsetY)
            }
        }
        .widgetURL(URL(string: "watchapp://hrv-scysc?hrv=\(entry.value)&time=\(formatTime(entry.date))&shichen=\(getHourIndex(for: entry.date))"))
    }

    // 获取十二时辰对应的图片名称
    private func getHourImageName(for date: Date) -> String {
        let hourIndex = getHourIndex(for: date)
        return "\(hourIndex)"
    }

    // 获取十二时辰索引（1-12）
    private func getHourIndex(for date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)

        // 将24小时制转换为12个时辰
        // 子时(23-1)、丑时(1-3)、寅时(3-5)、卯时(5-7)、辰时(7-9)、巳时(9-11)
        // 午时(11-13)、未时(13-15)、申时(15-17)、酉时(17-19)、戌时(19-21)、亥时(21-23)
        switch hour {
        case 23, 0: return 1  // 子时
        case 1, 2: return 2   // 丑时
        case 3, 4: return 3   // 寅时
        case 5, 6: return 4   // 卯时
        case 7, 8: return 5   // 辰时
        case 9, 10: return 6  // 巳时
        case 11, 12: return 7 // 午时
        case 13, 14: return 8 // 未时
        case 15, 16: return 9 // 申时
        case 17, 18: return 10 // 酉时
        case 19, 20: return 11 // 戌时
        case 21, 22: return 12 // 亥时
        default: return 1
        }
    }

    // 计算进度点位置（避开断层）
    private func calculateDotPosition(geometry: GeometryProxy) -> CGFloat {
        // HRV范围：0-88ms，越低压力越高，越往右
        let minHRV: CGFloat = 0
        let maxHRV: CGFloat = 88
        let currentHRV = CGFloat(entry.value)

        // 确保值在范围内
        let clampedHRV = max(minHRV, min(maxHRV, currentHRV))

        // 计算进度百分比（HRV越高，进度越靠右，表示压力越小）
        // 但是我们要反转，让压力高的（HRV低的）在右边
        let progress = 1.0 - (clampedHRV / maxHRV)

        // 使用实际进度条宽度（就是组件宽度）
        let actualProgressWidth = geometry.size.width

        // 进度条参数设置
        let gapWidth: CGFloat = 8            // 断层宽度
        let gapOffset: CGFloat = 6           // 断层两侧各6px需要避开

        // 计算1/3和2/3断层位置
        let gap1Position = actualProgressWidth / 3      // 1/3位置
        let gap2Position = actualProgressWidth * 2 / 3  // 2/3位置

        // 计算基础偏移量
        let baseOffsetX = (progress * actualProgressWidth) - (actualProgressWidth / 2)

        // 转换为相对于进度条左端的位置
        let positionFromLeft = baseOffsetX + (actualProgressWidth / 2)

        // 检查是否接近断层并调整位置
        var adjustedPosition = positionFromLeft

        // 检查1/3断层
        if abs(positionFromLeft - gap1Position) <= gapOffset {
            if positionFromLeft < gap1Position {
                // 在断层左侧，向左偏移
                adjustedPosition = gap1Position - gapOffset
            } else {
                // 在断层右侧，向右偏移
                adjustedPosition = gap1Position + gapOffset
            }
        }
        // 检查2/3断层
        else if abs(positionFromLeft - gap2Position) <= gapOffset {
            if positionFromLeft < gap2Position {
                // 在断层左侧，向左偏移
                adjustedPosition = gap2Position - gapOffset
            } else {
                // 在断层右侧，向右偏移
                adjustedPosition = gap2Position + gapOffset
            }
        }

        // 确保不溢出边界
        adjustedPosition = max(0, min(actualProgressWidth, adjustedPosition))

        // 转换回相对于中心的偏移量
        let finalOffsetX = adjustedPosition - (actualProgressWidth / 2)

        return finalOffsetX
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVSCYScComplication: Widget {
    let kind: String = "HRVSCYScComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVSCYScComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVSCYScComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("十二时辰HRV监测-SCYSc")
        .description("结合十二时辰换图和HRV压力监测")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HRVSCYScComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVSCYScComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVSCYScComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
