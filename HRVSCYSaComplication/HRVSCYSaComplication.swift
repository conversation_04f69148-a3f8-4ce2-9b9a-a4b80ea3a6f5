//
//  HRVSCYSaComplication.swift
//  HRVSCYSaComplication
//
//  Created by Mac2024 on 2025/6/17.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }

    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [HRVComplicationModel] = []
        let currentDate = Date()

        // 生成24小时的时间线，每10分钟更新一次检测是否需要切换时辰
        for minuteOffset in stride(from: 0, to: 24 * 60, by: 10) {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            // 模拟HRV数据，实际应用中应该从HealthKit获取
            let mockHrvValue = Int.random(in: 20...60)
            let entry = HRVComplicationModel(date: entryDate, value: mockHrvValue, steps: 88)
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 25, steps: 88)
}

struct HRVSCYSaComplicationEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景：十二时辰图片
                Image(getHourImageName(for: entry.date))
                    .resizable()
                    .scaledToFill()
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .clipped()
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            }
        }
        .widgetURL(URL(string: "watchapp://hrv-scysa?time=\(formatTime(entry.date))&hrv=\(entry.value)"))
    }

    // 获取十二时辰对应的图片名称
    private func getHourImageName(for date: Date) -> String {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)

        // 将24小时制转换为12个时辰
        // 子时(23-1)、丑时(1-3)、寅时(3-5)、卯时(5-7)、辰时(7-9)、巳时(9-11)
        // 午时(11-13)、未时(13-15)、申时(15-17)、酉时(17-19)、戌时(19-21)、亥时(21-23)
        let hourIndex: Int
        switch hour {
        case 23, 0: hourIndex = 1  // 子时
        case 1, 2: hourIndex = 2   // 丑时
        case 3, 4: hourIndex = 3   // 寅时
        case 5, 6: hourIndex = 4   // 卯时
        case 7, 8: hourIndex = 5   // 辰时
        case 9, 10: hourIndex = 6  // 巳时
        case 11, 12: hourIndex = 7 // 午时
        case 13, 14: hourIndex = 8 // 未时
        case 15, 16: hourIndex = 9 // 申时
        case 17, 18: hourIndex = 10 // 酉时
        case 19, 20: hourIndex = 11 // 戌时
        case 21, 22: hourIndex = 12 // 亥时
        default: hourIndex = 1
        }

        return "\(hourIndex)"
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVSCYSaComplication: Widget {
    let kind: String = "HRVSCYSaComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVSCYSaComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVSCYSaComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("十二时辰换图-SCYSa")
        .description("根据十二时辰显示对应背景图片")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HRVSCYSaComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVSCYSaComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVSCYSaComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
