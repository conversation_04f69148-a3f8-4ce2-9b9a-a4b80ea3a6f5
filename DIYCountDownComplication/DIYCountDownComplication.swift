//
//  DIYCountDownComplication.swift
//  DIYCountDownComplication
//
//  Created by Mac2024 on 2025/4/8.
//

import WidgetKit
import SwiftUI

import WidgetKit
import SwiftUI

// MARK: - 共享实用工具类
class WidgetUtilities {
    static let shared = WidgetUtilities()
    let appGroupIdentifier = "group.com.watchfaceos.flytheme"
    
    // 图片文件名
    private let countDownImageFileName = "countDownWeightName.png"
    private let countDownEndImageFileName = "countDownEndWeightName.png"
    
    private let defaultImageFileName = "selectedImage.png" // 兼容旧版
    
    private let countDownName = "countDown" // 兼容旧版
    private let countDownEndName = "countDownEnd" // 兼容旧版
    
    private init() {}
    
    // 记录日志消息
    func logMessage(_ message: String, source: String) {
        print("📱 \(source): \(message)")
    }
    
    // 获取图片文件URL
    private func getImageFileURL(forWidget widgetType: String? = nil) -> URL? {
        guard let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) else {
            return nil
        }
        
        // 根据小组件类型返回不同的文件路径
        if let widgetType = widgetType {
            if widgetType == countDownName {
                let url = containerURL.appendingPathComponent(countDownImageFileName)
                logMessage("正在获取上班时间图片URL: \(url.path)", source: "WidgetUtils")
                return url
            }
            else if widgetType == countDownEndName {
                let url = containerURL.appendingPathComponent(countDownEndImageFileName)
                logMessage("正在获取非上班时间图片URL: \(url.path)", source: "WidgetUtils")
                return url
            }
        }
        
        // 默认返回旧版文件名（兼容性）
        let url = containerURL.appendingPathComponent(defaultImageFileName)
        logMessage("正在获取默认图片URL: \(url.path)", source: "WidgetUtils")
        return url
    }
    
    // 从App Group加载图片
    func loadImageFromAppGroup(isWorkTime: Bool = false) -> UIImage? {
        // 上班时间用countDownName，非上班时间用countDownEndName
        let widgetType = isWorkTime ? countDownName : countDownEndName
        let source = isWorkTime ? "工作时间图片" : "非工作时间图片"
        
        guard let imageURL = getImageFileURL(forWidget: widgetType) else {
            logMessage("无法获取App Group路径", source: source)
            return nil
        }
        
        logMessage("检查图片文件: \(imageURL.path)", source: source)
        
        if FileManager.default.fileExists(atPath: imageURL.path) {
            logMessage("图片文件存在", source: source)
            if let imageData = try? Data(contentsOf: imageURL),
               let image = UIImage(data: imageData) {
                logMessage("成功加载图片: \(widgetType)", source: source)
                return image
            } else {
                logMessage("无法解析图片数据", source: source)
            }
        } else {
            logMessage("图片文件不存在", source: source)
            
            // 尝试加载通用图片
            logMessage("尝试加载通用图片", source: source)
            if let defaultURL = getImageFileURL(),
               FileManager.default.fileExists(atPath: defaultURL.path),
               let imageData = try? Data(contentsOf: defaultURL),
               let image = UIImage(data: imageData) {
                logMessage("成功加载通用图片", source: source)
                return image
            }
        }
        
        // 回退到内置的默认图片
        logMessage("使用默认图片", source: source)
        return UIImage(named: "01")
    }
    
    //获取字符串Url
    func getStringFileURL() -> String {
        let source = "CountDown"
        var selectedNumber = "countDown#08:00#18:00#工作日" // 提供一个合理的默认值
        // 直接从 App Group 文件读取手表存储的文件
        if let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: appGroupIdentifier) {
            let numberURL = containerURL.appendingPathComponent("countDown.txt")
            logMessage("检查文件: \(numberURL.path)", source: source)
            
            if FileManager.default.fileExists(atPath: numberURL.path) {
                logMessage("文件存在", source: source)
                if let numberData = try? Data(contentsOf: numberURL),
                   let number = String(data: numberData, encoding: .utf8) {
                    selectedNumber = number
                    logMessage("直接从文件读取到的字符串: \(number)", source: source)
                } else {
                    logMessage("无法解析文件内容", source: source)
                }
            } else {
                logMessage("文件不存在，使用默认值", source: source)
            }
        } else {
            logMessage("无法获取 App Group 路径", source: source)
        }
        logMessage("倒计时使用的字符串: \(selectedNumber)", source: source)
        return selectedNumber
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let image: UIImage?
//    let imageEnd: UIImage?
    let countdownText: String?
    let isWorkTime: Bool
}

struct Provider: TimelineProvider {
    let source = "小组件"
    
    private func logMessage(_ message: String) {
        WidgetUtilities.shared.logMessage(message, source: source)
    }
   
    func placeholder(in context: Context) -> SimpleEntry {
        logMessage("创建占位符")
        let image = WidgetUtilities.shared.loadImageFromAppGroup(isWorkTime: false)
        return SimpleEntry(date: Date(), image: image ,countdownText: "", isWorkTime: false)
    }
    
    //现在的逻辑是DIYCountDownComplication在展示的时候会获取字符串getStringFileURL，字符串解析后我们可以知道用户的上班时间（比如用户设置的上班时间是周一到周五8:00-18:00），根据当前是否是上班时间展示不同的样式，如果是上班时间，则展示countDownImageFileName的图片，否则展示countDownEndName的图片。 现在整个逻辑已经写完了，但是设置表盘的时候，表盘上的图片没有展示出来。帮我检查相关逻辑是否有错误。
    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        logMessage("获取快照")
        let str = WidgetUtilities.shared.getStringFileURL()
        
        logMessage("str 获取到的字符串= \(str)")
     
        let schedule = parseWorkSchedule(str)
        logMessage("解析后的时间表: 开始=\(schedule.startTime), 结束=\(schedule.endTime), 工作日=\(schedule.workDays)")

        let (countdownText, isWorkTime) = calculateCountdown(currentDate: Date(), schedule: schedule)
        
        if isWorkTime {
            logMessage("当前是工作时间，倒计时文本 = \(countdownText ?? "无")")
        } else {
            logMessage("当前不是工作时间")
        }
    
        
        // 根据是否是工作时间加载不同的图片
        let image = WidgetUtilities.shared.loadImageFromAppGroup(isWorkTime: isWorkTime)
        
        let entry = SimpleEntry(date: Date(), image: image, countdownText: countdownText, isWorkTime: false)
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        logMessage("开始获取时间线")
        
        var entries: [SimpleEntry] = []
        let currentDate = Date()
        let str = WidgetUtilities.shared.getStringFileURL()
        let schedule = parseWorkSchedule(str)

        // 只创建未来5分钟内的每分钟时间点
        for minuteOffset in 0..<5 {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            let (countdownText, isWorkTime) = calculateCountdown(currentDate: entryDate, schedule: schedule)
            
            // 根据是否是工作时间加载不同的图片
            let image = WidgetUtilities.shared.loadImageFromAppGroup(isWorkTime: isWorkTime)
            
            let entry = SimpleEntry(date: entryDate, image: image, countdownText: countdownText, isWorkTime: isWorkTime)
            entries.append(entry)
        }
        
        logMessage("创建时间线，条目数量: \(entries.count)")
        // 设置5分钟后重新加载时间线
//        let nextUpdateDate = Calendar.current.date(byAdding: .minute, value: 5, to: currentDate)!
//        let timeline = Timeline(entries: entries, policy: .atEnd)
        
        let nextUpdateDate = Calendar.current.date(byAdding: .minute, value: 5, to: currentDate)!
        let timeline = Timeline(entries: entries, policy: .after(nextUpdateDate))
        
        completion(timeline)
    }
    

    //分解字符串
    private func parseWorkSchedule(_ str: String) -> (startTime: String, endTime: String, workDays: Set<String>) {
        let components = str.components(separatedBy: "#")
        guard components.count >= 4 else {
            return ("", "", [])
        }
        
        let startTime = components[1]
        let endTime = components[2]
        let workDaysStr = components[3]
        
        logMessage("startTime = \(startTime)")
        logMessage("endTime = \(endTime)")
        logMessage("workDaysStr = \(workDaysStr)")
        
        
        var workDays: Set<String> = []
        if workDaysStr == "每一天" {
            workDays = Set(["周一", "周二", "周三", "周四", "周五", "周六", "周日"])
        } else if workDaysStr == "工作日" {
            workDays = Set(["周一", "周二", "周三", "周四", "周五"])
        } else {
            workDays = Set(workDaysStr.components(separatedBy: "、"))
        }
        
        return (startTime, endTime, workDays)
    }
    
    
    //判断是否工作时间 如果是工作时间返回字符串
    private func calculateCountdown(currentDate: Date, schedule: (startTime: String, endTime: String, workDays: Set<String>)) -> (String?, Bool) {
        guard isCurrentDayWorkDay(schedule.workDays),
              let startTime = timeStringToDate(schedule.startTime, baseDate: currentDate),
              let endTime = timeStringToDate(schedule.endTime, baseDate: currentDate) else {
            return ("", false)
        }
        
        if currentDate < startTime || currentDate > endTime {
            return ("", false)
        }
        
        let timeInterval = endTime.timeIntervalSince(currentDate)
        let hours = Int(timeInterval) / 3600
        let minutes = Int(timeInterval) / 60 % 60
   
        
        if hours > 0 {
            return ("\(hours)h\(minutes)min", true)
        } else if minutes > 0 {
            return ("\(minutes)min", true)
        } else {
            return ("1min", true)
        }
    }
    
    
    private func isCurrentDayWorkDay(_ workDays: Set<String>) -> Bool {
        let calendar = Calendar.current
        let weekday = calendar.component(.weekday, from: Date())
        let weekdayMap = [1: "周日", 2: "周一", 3: "周二", 4: "周三", 5: "周四", 6: "周五", 7: "周六"]
        guard let currentWeekday = weekdayMap[weekday] else { return false }
        return workDays.contains(currentWeekday)
    }
    
    private func timeStringToDate(_ timeStr: String, baseDate: Date) -> Date? {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        guard let time = formatter.date(from: timeStr) else { return nil }
        
        let calendar = Calendar.current
        var components = calendar.dateComponents([.year, .month, .day], from: baseDate)
        let timeComponents = calendar.dateComponents([.hour, .minute], from: time)
        
        components.hour = timeComponents.hour
        components.minute = timeComponents.minute
        
        return calendar.date(from: components)
    }
}

struct DIYCountDownComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        ZStack {
            // 用户设置的背景图片
            if let image = entry.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            }
            
            // 倒计时文字覆盖层
            if let countdownText = entry.countdownText, !countdownText.isEmpty {
                HStack(spacing: 0) {
                    Spacer()
                    
                    Text(countdownText)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.top, 40)
                        .padding(.trailing, 17)
                        .multilineTextAlignment(.trailing)
                        .shadow(color: .black, radius: 1, x: 0, y: 0)
                }
            }
        }
    }
}

@main
struct DIYCountDownComplication: Widget {
    let kind: String = "DIYCountDownComplication"
    
    // 初始化和日志记录
    init() {
        print("⌚️ 倒计时小组件初始化 - Kind: \(kind)")
        // 记录当前正在运行的小组件信息
        let message = "倒计时小组件启动: \(kind) at \(Date())"
        print("⌚️ CountDown Widget记录日志: \(message)")
    }
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                DIYCountDownComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                DIYCountDownComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("表盘市场-上班倒计时")
        .description("显示用户在App中选择的图片")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本
struct DIYCountDownComplication_Previews: PreviewProvider {
    static var previews: some View {
        let previewImage = UIImage(named: "01")
        let entry = SimpleEntry(date: Date(), image: previewImage, countdownText: nil, isWorkTime: false)
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            DIYCountDownComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            DIYCountDownComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
