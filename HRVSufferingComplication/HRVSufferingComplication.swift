//
//  HRVSufferingComplication.swift
//  HRVSufferingComplication
//
//  Created by Mac2024 on 2025/5/12.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVSufferingComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    // MARK: - 布局参数（可根据需要调整）
    // 图片顶部间距
    let imageTopPadding: CGFloat = 10
    // 图片水平间距
    let imageHorizontalPadding: CGFloat = 0
    // 文字顶部间距
    let textTopPadding: CGFloat = 0
    // 文字水平间距
    let textHorizontalPadding: CGFloat = 12
    // 文字间的垂直间距
    let textVerticalPadding: CGFloat = 4
    
    var body: some View {
        ZStack(alignment: .top) {
            // 底层图片
            Image(HRVManager.shared.showZZEHimage(HRVValue: entry.value).imgName)
                .resizable()
                .scaledToFit()
                .padding(.top, imageTopPadding)
                .padding(.horizontal, imageHorizontalPadding)
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            
            // 上层文字
            HStack {
                // 左侧：心形图标 + HRV值
                HStack(spacing: 2) {
                    Image(systemName: "heart.fill")
                        .foregroundColor(Color(red: 255/255, green: 75/255, blue: 160/255))
                        .font(.system(size: 11))
                    Text("HRV \(entry.value)ms")
                        .font(.system(size: 11, weight: .bold))
                }
                .foregroundStyle(.white)
                
                Spacer()
                
                // 右侧：时间
                Text("\(formatTime(entry.date))")
                    .foregroundStyle(.white)
                    .font(.system(size: 11, weight: .bold))
            }
            .padding(.horizontal, textHorizontalPadding)
            .padding(.top, textTopPadding)
            .padding(.vertical, textVerticalPadding)
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVSufferingComplication: Widget {
    let kind: String = "HRVSufferingComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVSufferingComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVSufferingComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("没苦硬吃-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本
struct HRVSufferingComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVSufferingComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVSufferingComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
