//
//  HRVKASTComplication.swift
//  HRVKASTComplication
//
//  Created by Mac2024 on 2025/4/7.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVKASTComplicationEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode
    let progressWidth = 60.0
    
    var body: some View {
        ZStack {
            // 背景图片
            Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).imgName)
                .resizable()
                .scaledToFill()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            
            // 内容布局
            VStack(alignment: .leading, spacing: 0) {
                HStack {
                    Text(entry.date.formatted(.dateTime.month(.twoDigits).day(.twoDigits).hour().minute()))
                        .foregroundStyle(.black)
                        .font(.system(size: 9, weight: .semibold))
                        .padding(.top, 31)
                        .padding(.leading, 32)

                }

                ZStack {
                    Image("progress")
                        .resizable()
                        .frame(width: progressWidth, height: 3)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    // 计算偏移量
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                    Image("currentValue")
                        .resizable()
                        .frame(width: 5, height: 5)
                        .offset(x: offsetX, y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
             
                }
                .padding(.leading, 28)
                .padding(.bottom, 0)
                .padding(.top, 2)
                
                HStack(spacing: 9) {
                    Text("心理压力\((HRVManager.shared.getSJMHHRVColor(value: entry.value).yaliHrv))%")
                        .foregroundStyle(Color(red: 255/255, green: 247/255, blue: 65/255, opacity: 1.0))
                        .font(.system(size: 9, weight: .bold))
                        .padding(.leading, 32)
                    
                    Spacer()
                   
                }
            }
        }.widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVKASTComplication: Widget {
    let kind: String = "HRVKASTComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVKASTComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVKASTComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("可爱水豚-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本 这里为啥都是卡皮吧啦的
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVKASTComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVKASTComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
