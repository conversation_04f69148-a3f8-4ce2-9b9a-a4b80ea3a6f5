//
//  HRVSCYSbComplication.swift
//  HRVSCYSbComplication
//
//  Created by Mac2024 on 2025/6/17.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }

    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [HRVComplicationModel] = []
        let currentDate = Date()

        // 生成24小时的时间线，每10分钟更新一次
        for minuteOffset in stride(from: 0, to: 24 * 60, by: 10) {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            // 模拟HRV数据，实际应用中应该从HealthKit获取
            let mockHrvValue = Int.random(in: 15...50)
            let entry = HRVComplicationModel(date: entryDate, value: mockHrvValue, steps: 88)
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 23, steps: 88)
}

struct HRVSCYSbComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode

    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 第一层：HRV数值和时间 - 贴上边
                HStack {
                    Text("\(entry.value)ms")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.primary)

                    Spacer()

                    Text(formatTime(entry.date))
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.primary)
                }
                .padding(.horizontal, 4)
                .padding(.top, 2)

                Spacer()

                // 第二层：进度条和进度点 - 居中
                ZStack {
                    // 进度条背景 - 保持原始宽高比，贴边显示
                    Image("processing")
                        .resizable()
                        .scaledToFit()
                        .frame(width: geometry.size.width - 8)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)

                    // 进度点 - 保持原始宽高比，稍微掩盖进度条
                    Image("dot")
                        .resizable()
                        .scaledToFit()
                        .frame(height: 12) // 增大尺寸，让点稍微掩盖进度条
                        .offset(x: calculateDotPosition(geometry: geometry), y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .padding(.horizontal, 4)

                Spacer()

                // 第三层：HRV等级描述 - 贴下边
                Text(getHRVLevelDescription(value: entry.value))
                    .font(.system(size: 14, weight: .medium)) // 增大字体
                    .foregroundColor(getHRVLevelColor(value: entry.value))
                    .multilineTextAlignment(.center)
                    .lineLimit(1)
                    .padding(.horizontal, 4)
                    .padding(.bottom, 2)
            }
        }
        .widgetURL(URL(string: "watchapp://hrv-scysb?hrv=\(entry.value)&time=\(formatTime(entry.date))"))
    }

    // 计算进度点位置
    private func calculateDotPosition(geometry: GeometryProxy) -> CGFloat {
        // HRV范围：0-100ms，越低压力越高，越往右
        let minHRV: CGFloat = 0
        let maxHRV: CGFloat = 100
        let currentHRV = CGFloat(entry.value)

        // 确保值在范围内
        let clampedHRV = max(minHRV, min(maxHRV, currentHRV))

        // 计算进度百分比（HRV越高，进度越靠右，表示压力越小）
        // 但是我们要反转，让压力高的（HRV低的）在右边
        let progress = 1.0 - (clampedHRV / maxHRV)

        // 计算可用宽度（减去padding）
        let availableWidth = geometry.size.width - 16

        // 计算偏移量（从左端到右端）
        let offsetX = (progress * availableWidth) - (availableWidth / 2)

        return offsetX
    }

    // 获取HRV等级描述
    private func getHRVLevelDescription(value: Int) -> String {
        switch value {
        case ...19:
            return "注意压力·放平心态"
        case 20...39:
            return "心平气和·释放压力"
        case 40..<70:
            return "心情愉悦·适当放松"
        default:
            return "元气满满·保持好心情"
        }
    }

    // 获取HRV等级颜色
    private func getHRVLevelColor(value: Int) -> Color {
        switch value {
        case ...19:
            return Color(hex: "F89433") // 橙红色 - 压力过载
        case 20...39:
            return Color(hex: "FABB26") // 橙黄色 - 注意压力
        case 40..<70:
            return Color(hex: "8CE629") // 绿色 - 悠然自在
        default:
            return Color(hex: "3DDAC4") // 青绿色 - 活力满满
        }
    }

    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVSCYSbComplication: Widget {
    let kind: String = "HRVSCYSbComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVSCYSbComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVSCYSbComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("HRV压力监测-SCYSb")
        .description("显示HRV数值、压力等级和进度条")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HRVSCYSbComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVSCYSbComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVSCYSbComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}


// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
