//
//  HourGFYSJZComplication.swift
//  HourGFYSJZComplication
//
//  Created by Mac2024 on 2025/6/3.
//

import WidgetKit
import SwiftUI

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HourEntry {
        HourEntry(date: Date())
    }

    func getSnapshot(in context: Context, completion: @escaping (HourEntry) -> ()) {
        let entry = HourEntry(date: Date())
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [HourEntry] = []
        let currentDate = Date()
        
        // 生成未来24小时的时间线，每10分钟更新一次
        for minuteOffset in stride(from: 0, to: 24 * 60, by: 10) {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            let entry = HourEntry(date: entryDate)
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct HourEntry: TimelineEntry {
    let date: Date
}

struct HourGFYSJZComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode

    var body: some View {
        GeometryReader { geometry in
            // 显示对应时辰的中医养生图片
            Image(getShichenImageName(for: entry.date))
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: geometry.size.width, height: geometry.size.height)
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                .clipped()
        }
        .widgetURL(URL(string: "watchapp://hour?time=\(formatTime(entry.date))"))
    }
    
    // 根据时间获取对应的十二时辰图片名称
    private func getShichenImageName(for date: Date) -> String {
        let hour = Calendar.current.component(.hour, from: date)
        
        switch hour {
        case 23, 0:
            return "1"   // 子时 (23:00-01:00)
        case 1, 2:
            return "2"   // 丑时 (01:00-03:00)
        case 3, 4:
            return "3"   // 寅时 (03:00-05:00)
        case 5, 6:
            return "4"   // 卯时 (05:00-07:00)
        case 7, 8:
            return "5"   // 辰时 (07:00-09:00)
        case 9, 10:
            return "6"   // 巳时 (09:00-11:00)
        case 11, 12:
            return "7"   // 午时 (11:00-13:00)
        case 13, 14:
            return "8"   // 未时 (13:00-15:00)
        case 15, 16:
            return "9"   // 申时 (15:00-17:00)
        case 17, 18:
            return "10"  // 酉时 (17:00-19:00)
        case 19, 20:
            return "11"  // 戌时 (19:00-21:00)
        case 21, 22:
            return "12"  // 亥时 (21:00-23:00)
        default:
            return "1"   // 默认返回子时
        }
    }
    
    // 根据时间获取对应的十二时辰名称
    private func getShichenName(for date: Date) -> String {
        let hour = Calendar.current.component(.hour, from: date)
        
        switch hour {
        case 23, 0:
            return "子时"  // 23:00-01:00
        case 1, 2:
            return "丑时"  // 01:00-03:00
        case 3, 4:
            return "寅时"  // 03:00-05:00
        case 5, 6:
            return "卯时"  // 05:00-07:00
        case 7, 8:
            return "辰时"  // 07:00-09:00
        case 9, 10:
            return "巳时"  // 09:00-11:00
        case 11, 12:
            return "午时"  // 11:00-13:00
        case 13, 14:
            return "未时"  // 13:00-15:00
        case 15, 16:
            return "申时"  // 15:00-17:00
        case 17, 18:
            return "酉时"  // 17:00-19:00
        case 19, 20:
            return "戌时"  // 19:00-21:00
        case 21, 22:
            return "亥时"  // 21:00-23:00
        default:
            return "子时"  // 默认返回子时
        }
    }
    
    // 时间格式化函数
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// Color 扩展，支持十六进制颜色
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

@main
struct HourGFYSJZComplication: Widget {
    let kind: String = "HourGFYSJZComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HourGFYSJZComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HourGFYSJZComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("时辰换图-古风养生卷轴")
        .description("根据中国古代十二时辰显示不同背景")
        .supportedFamilies([.accessoryRectangular])
    }
}

struct HourGFYSJZComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HourEntry(date: Date())
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HourGFYSJZComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HourGFYSJZComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
