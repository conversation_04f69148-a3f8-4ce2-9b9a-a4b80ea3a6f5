//
//  HRVHourQGYSComplication.swift
//  HRVHourQGYSComplication
//
//  Created by Mac2024 on 2025/5/26.
//

import WidgetKit
import SwiftUI
import HealthKit

// 真实的 Combo 管理器
class RealComboManager {
    static let shared = RealComboManager()
    
    private let userDefaults = UserDefaults(suiteName: "group.com.watchfaceos.flytheme") // 使用 App Group
    private let maxHistoryCount = 50 // 保留最近50条记录
    private let minIntervalMinutes: TimeInterval = 60 // 最小记录间隔（分钟）
    
    private init() {}
    
    // 压力状态记录结构
    struct StressRecord: Codable {
        let level: String
        let timestamp: Date
        let hrvValue: Int
    }
    
    // 检查是否在醒着的时间段
    private func isAwakeTime() -> Bool {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: Date())
        return hour >= 6 && hour < 22 // 早上6点到晚上10点
    }
    
    // 检查是否可以记录新的压力状态
    private func canRecordNewStress(level: String, hrvValue: Int) -> Bool {
        // 如果不在醒着的时间段，不记录
        guard isAwakeTime() else { return false }
        
        let history = getStressHistory()
        
        // 如果没有历史记录，可以记录
        guard let lastRecord = history.last else { return true }
        
        // 如果压力等级发生变化，立即记录
        if lastRecord.level != level {
            return true
        }
        
        // 如果HRV值变化超过阈值（例如10点），立即记录
        let hrvDifference = abs(lastRecord.hrvValue - hrvValue)
        if hrvDifference >= 10 {
            return true
        }
        
        // 如果以上条件都不满足，检查时间间隔
        let timeSinceLastRecord = Date().timeIntervalSince(lastRecord.timestamp)
        return timeSinceLastRecord >= minIntervalMinutes * 60
    }
    
    // 添加新的压力状态记录
    func addStressRecord(level: String, hrvValue: Int) {
        guard canRecordNewStress(level: level, hrvValue: hrvValue) else { return }
        
        var history = getStressHistory()
        
        let newRecord = StressRecord(
            level: level,
            timestamp: Date(),
            hrvValue: hrvValue
        )
        
        history.append(newRecord)
        
        // 保持历史记录数量在限制范围内
        if history.count > maxHistoryCount {
            history.removeFirst(history.count - maxHistoryCount)
        }
        
        saveStressHistory(history)
    }
    
    // 获取当前连击数
    func getCurrentCombo(for level: String) -> Int {
        let history = getStressHistory()
        guard !history.isEmpty else { return 1 }
        
        var combo = 0
        
        // 从最新记录开始向前计算连击
        for record in history.reversed() {
            if record.level == level {
                combo += 1
            } else {
                break
            }
        }
        
        return max(combo, 1) // 至少返回1
    }
    
    // 获取压力状态历史记录
    private func getStressHistory() -> [StressRecord] {
        guard let data = userDefaults?.data(forKey: "stressHistory"),
              let history = try? JSONDecoder().decode([StressRecord].self, from: data) else {
            return []
        }
        return history
    }
    
    // 保存压力状态历史记录
    private func saveStressHistory(_ history: [StressRecord]) {
        if let data = try? JSONEncoder().encode(history) {
            userDefaults?.set(data, forKey: "stressHistory")
        }
    }
    
    // 清理过期记录（可选，比如清理7天前的记录）
    func cleanOldRecords() {
        let history = getStressHistory()
        let sevenDaysAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
        
        let filteredHistory = history.filter { $0.timestamp >= sevenDaysAgo }
        saveStressHistory(filteredHistory)
    }
    
    // 获取统计信息（可选功能）
    func getComboStats() -> (maxCombo: Int, currentStreak: String, totalRecords: Int) {
        let history = getStressHistory()
        
        // 计算最大连击数
        var maxCombo = 0
        var currentCombo = 0
        var lastLevel = ""
        
        for record in history {
            if record.level == lastLevel {
                currentCombo += 1
            } else {
                maxCombo = max(maxCombo, currentCombo)
                currentCombo = 1
                lastLevel = record.level
            }
        }
        maxCombo = max(maxCombo, currentCombo)
        
        // 当前连击状态
        let currentStreak = history.isEmpty ? "无记录" : "\(lastLevel) x \(currentCombo)"
        
        return (maxCombo, currentStreak, history.count)
    }
}

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        var entries: [HRVComplicationModel] = []
        let currentDate = Date()
        
        // 生成24小时的时间线，每10分钟更新一次
        for minuteOffset in stride(from: 0, to: 24 * 60, by: 10) {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset, to: currentDate)!
            // 模拟HRV数据，实际应用中应该从HealthKit获取
            let mockHrvValue = Int.random(in: 20...60)
            let entry = HRVComplicationModel(date: entryDate, value: mockHrvValue, steps: 88)
            entries.append(entry)
        }

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 45, steps: 88)
}

struct HRVHourQGYSComplicationEntryView: View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    // 可调整的布局参数
    private let textOffset: CGFloat = 32.0       // 文本垂直位置偏移（负值向上，正值向下）
    private let textHorizontalOffset: CGFloat = -56.0  // 文本水平位置偏移（负值向左，正值向右）
    private let textAlignment: HorizontalAlignment = .center  // 文本水平对齐方式 (.leading/.center/.trailing)
    private let textSpacing: CGFloat = 4.0        // 元素之间的间距
    private let fontSize: CGFloat = 14.0          // 压力状态文字的字体大小
    private let fontWeight: Font.Weight = .bold   // 文字粗细
    private let shadowRadius: CGFloat = 2.0       // 文字阴影的大小
    private let shadowOffset: CGPoint = CGPoint(x: 1, y: 1) // 阴影偏移量
    private let textPaddingHorizontal: CGFloat = 8.0  // 文字水平内边距
    private let textPaddingVertical: CGFloat = 4.0    // 文字垂直内边距
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景：十二时辰图片
                Image(getHourImageName(for: entry.date))
                    .resizable()
                    .scaledToFill()
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                
                // 前景：HRV 压力状态文字
                let currentLevel = getHRVStressLevel(value: entry.value)
                
                // 添加当前记录到历史并获取真实连击数
                // 注意：这里会根据HRV值变化或时间间隔自动判断是否需要更新记录
                let _ = RealComboManager.shared.addStressRecord(level: currentLevel, hrvValue: entry.value)
                let realCombo = RealComboManager.shared.getCurrentCombo(for: currentLevel)
                
                // 使用绝对定位来放置文本
                Text("\(currentLevel) x \(realCombo)")
                    .font(.system(size: fontSize, weight: fontWeight))
                    .foregroundColor(.clear)
                    .background(
                        LinearGradient(
                            colors: [getHourStartColor(for: entry.date), getHourEndColor(for: entry.date)],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                        .mask(
                            Text("\(currentLevel) x \(realCombo)")
                                .font(.system(size: fontSize, weight: fontWeight))
                        )
                    )
                    .shadow(color: .black.opacity(0.8), radius: shadowRadius, x: shadowOffset.x, y: shadowOffset.y)
                    .padding(.horizontal, textPaddingHorizontal)
                    .padding(.vertical, textPaddingVertical)
                    .position(
                        x: geometry.size.width / 2 + textHorizontalOffset,
                        y: geometry.size.height / 2 + textOffset
                    )
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
        .onAppear {
            // 清理过期记录（可选）
            RealComboManager.shared.cleanOldRecords()
        }
    }
    
    // 获取十二时辰对应的图片名称
    private func getHourImageName(for date: Date) -> String {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        
        // 将24小时制转换为12个时辰
        // 子时(23-1)、丑时(1-3)、寅时(3-5)、卯时(5-7)、辰时(7-9)、巳时(9-11)
        // 午时(11-13)、未时(13-15)、申时(15-17)、酉时(17-19)、戌时(19-21)、亥时(21-23)
        let hourIndex: Int
        switch hour {
        case 23, 0: hourIndex = 0  // 子时
        case 1, 2: hourIndex = 1   // 丑时
        case 3, 4: hourIndex = 2   // 寅时
        case 5, 6: hourIndex = 3   // 卯时
        case 7, 8: hourIndex = 4   // 辰时
        case 9, 10: hourIndex = 5  // 巳时
        case 11, 12: hourIndex = 6 // 午时
        case 13, 14: hourIndex = 7 // 未时
        case 15, 16: hourIndex = 8 // 申时
        case 17, 18: hourIndex = 9 // 酉时
        case 19, 20: hourIndex = 10 // 戌时
        case 21, 22: hourIndex = 11 // 亥时
        default: hourIndex = 0
        }
        
        return "hour_\(hourIndex)"
    }
    
    // 获取十二时辰对应的起始颜色
    private func getHourStartColor(for date: Date) -> Color {
        let hourIndex = getHourIndex(for: date)
        let hourColors = getHourColors()
        return Color(hex: hourColors[hourIndex].start)
    }
    
    // 获取十二时辰对应的结束颜色
    private func getHourEndColor(for date: Date) -> Color {
        let hourIndex = getHourIndex(for: date)
        let hourColors = getHourColors()
        return Color(hex: hourColors[hourIndex].end)
    }
    
    // 获取时辰索引的辅助方法
    private func getHourIndex(for date: Date) -> Int {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        
        switch hour {
        case 23, 0: return 0  // 子时
        case 1, 2: return 1   // 丑时
        case 3, 4: return 2   // 寅时
        case 5, 6: return 3   // 卯时
        case 7, 8: return 4   // 辰时
        case 9, 10: return 5  // 巳时
        case 11, 12: return 6 // 午时
        case 13, 14: return 7 // 未时
        case 15, 16: return 8 // 申时
        case 17, 18: return 9 // 酉时
        case 19, 20: return 10 // 戌时
        case 21, 22: return 11 // 亥时
        default: return 0
        }
    }
    
    // 获取时辰颜色配置的辅助方法
    private func getHourColors() -> [(start: String, end: String)] {
        return [
            ("ff9c00", "fffd3d"), // 子时 0
            ("ff1398", "ffaada"), // 丑时 1
            ("00c3da", "d8fcff"), // 寅时 2
            ("946aff", "d1abff"), // 卯时 3
            ("65ff03", "f5ffb1"), // 辰时 4
            ("ff2121", "ff8278"), // 巳时 5
            ("ff9c00", "fffd3d"), // 午时 6
            ("ff1398", "ffaada"), // 未时 7
            ("00c3da", "d8fcff"), // 申时 8
            ("65ff03", "f5ffb1"), // 酉时 9
            ("ff2121", "ff8278"), // 戌时 10
            ("946aff", "d1abff")  // 亥时 11
        ]
    }
    
    // 获取 HRV 压力等级文字（五级）
    private func getHRVStressLevel(value: Int) -> String {
        switch value {
        case 0..<20:
            return "崩溃"
        case 20..<35:
            return "疲惫"
        case 35..<50:
            return "淡定"
        case 50..<70:
            return "安逸"
        default:
            return "无敌"
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVHourQGYSComplication: Widget {
    let kind: String = "HRVHourQGYSComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVHourQGYSComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVHourQGYSComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("时辰换图-养生HRV连击")
        .description("根据时辰和HRV值显示压力状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HRVHourQGYSComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVHourQGYSComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVHourQGYSComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
