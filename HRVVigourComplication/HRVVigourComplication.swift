//
//  HRVVigourComplication.swift
//  HRVVigourComplication
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/25.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }

    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        let entry = HRVComplicationModel.preview
        completion(entry)
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }

//    func relevances() async -> WidgetRelevances<Void> {
//        // Generate a list containing the contexts this widget is relevant in.
//    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}

struct HRVVigourComplicationEntryView : View {
    var entry: Provider.Entry
    // 检测渲染模式（彩色 / 染色 / 振动）
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        GeometryReader { outerGeometry in
            let widgetWidth = outerGeometry.size.width
            let widgetHeight = outerGeometry.size.height
            HStack() {
                Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).imgName)
                    .resizable()
                    .frame(width: widgetHeight, height: widgetHeight)
                    .scaledToFit()
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                
                VStack(alignment: .leading) {
                    let availableWidth = widgetWidth - widgetHeight
                    let topImgHeight = 31.0
                    Image(HRVManager.shared.showBGImageName(HRVValue: entry.value).textImgName)
                        .resizable()
                        .frame(width: availableWidth, height: topImgHeight)
                        .scaledToFit()
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    HStack(spacing: 0) {
                        Image("hrvImg")
                            .resizable()
                            .frame(width: 12, height: 10)
                            .widgetAccentable(renderingMode != .fullColor)
                            .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                        
                        Text("HRV \(entry.value)ms ")
                            .font(.system(size: 12, weight: .semibold))
                        
                        Text(entry.date, style: .time)
                            .font(.system(size: 12, weight: .semibold))
                    }
                    
                    let progressMaxHeight = 13.0
                    ZStack {
                        let progressWidth = availableWidth
                        Image("progress")
                            .resizable()
                            .frame(width: progressWidth, height: 9)
                            .widgetAccentable(renderingMode != .fullColor)
                            .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                        
                        let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                        // 计算偏移量
                        let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                        let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                        Image("currentValue")
                            .resizable()
                            .frame(width: progressMaxHeight, height: progressMaxHeight)
                            .offset(x: offsetX, y: 0)
                            .widgetAccentable(renderingMode != .fullColor)
                            .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    }
                    .frame(height: progressMaxHeight)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVVigourComplication: Widget {
    let kind: String = "HRVVigourComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVVigourComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVVigourComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("橙子-HRV")
        .description("根据HRV的值显示不同状态")
        .supportedFamilies([.accessoryRectangular])
    }
}

// watchOS 9.0 及以后的预览版本
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVVigourComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVVigourComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}

