//
//  HRVRijindoujinComplicationExtension.swift
//  HRVRijindoujinComplicationExtension
//
//  Created by Mac2024 on 2025/3/24.
//

import WidgetKit
import SwiftUI
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        let entry = HRVComplicationModel.preview
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimelineWithSteps { timeline in
            completion(timeline)
        }
    }
    
    //    func relevances() async -> WidgetRelevances<Void> {
    //        // Generate a list containing the contexts this widget is relevant in.
    //    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 88, steps: 88)
}
struct HRVRijindoujinComplicationEntryView : View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    var body: some View {
        GeometryReader { outerGeometry in
            let widgetWidth = outerGeometry.size.width

            let scale = widgetWidth / 181

            ZStack {
                Image("rjdj_imageBg")
                    .resizable()
                    .scaledToFill()
                    .padding([.top], 0 )
                    .padding([.bottom], 0)
                    .padding([.leading, .trailing], 0)
                    .widgetAccentable(renderingMode != .fullColor)
                    .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                
                HStack(spacing: 0) {
                    //用来在HStack中把VStack顶到右边去的
                    Spacer()
                        .frame(width: widgetWidth / 2 - 15 * scale)
        
                    VStack() {
                        Text("\(entry.steps)")
                            .font(.system(size: 9 * scale , weight: .medium))
                            .foregroundColor(.black)
                            .padding(.top, 24 * scale )
                            .frame(width: 40 * scale, alignment: .trailing)
                            .multilineTextAlignment(.trailing)
                   
                        Text("STEPS")
                            .font(.system(size: 9 * scale, weight: .medium))
                            .foregroundColor(.black)
                            .padding(.top, 1 )
                            .frame(width: 40 * scale, alignment: .trailing)
                            .multilineTextAlignment(.trailing)
                           
                    }
                    .frame(width: 45 * scale)
          
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }.widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

@main
struct HRVRijindoujinComplication: Widget {
    let kind: String = "HRVRijindoujinComplication"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVRijindoujinComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVRijindoujinComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("日进斗金-HRV")
        .description("记录并展示你的今日步数")
        .supportedFamilies([.accessoryRectangular])
    }
}


// watchOS 9.0 及以后的预览版本
struct HRVKPBLComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVRijindoujinComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVRijindoujinComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
