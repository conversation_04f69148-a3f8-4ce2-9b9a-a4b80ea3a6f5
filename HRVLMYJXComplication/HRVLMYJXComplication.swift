//
//  HRVLMYJXComplication.swift
//  HRVLMYJXComplication
//
//  Created by Mac2024 on 2025/6/4.
//

import SwiftUI
import WidgetKit
import HealthKit

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> HRVComplicationModel {
        HRVComplicationModel.preview
    }
    
    func getSnapshot(in context: Context, completion: @escaping (HRVComplicationModel) -> ()) {
        completion(HRVComplicationModel.preview)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        HRVManager.shared.packGetTimeline { timeline in
            completion(timeline)
        }
    }
}

struct HRVComplicationModel: TimelineEntry {
    let date: Date
    let value: Int
    let steps: Int
    static let preview = HRVComplicationModel(date: Date(), value: 25, steps: 88)
}

struct HRVLMYJXComplicationEntryView: View {
    var entry: Provider.Entry
    @Environment(\.widgetRenderingMode) private var renderingMode
    
    // 进度条参数（可调整）
    private let progressWidth: CGFloat = 65.0
    private let progressHeight: CGFloat = 9.0
    private let progressOffsetX: CGFloat = 18
    private let progressOffsetY: CGFloat = 3
    private let progressBottomPadding: CGFloat = 6
    
    // 进度点参数（可调整）
    private let dotSize: CGFloat = 10
    
    // 文字参数（可调整）
    private let textOffsetX: CGFloat = 20
    private let textOffsetY: CGFloat = 0
    private let textFontSize: CGFloat = 9
    
    var body: some View {
        ZStack {
            // 背景图片 - 占满但不溢出
            Image(getBackgroundImageName(for: entry.value))
                .resizable()
                .scaledToFit()
                .widgetAccentable(renderingMode != .fullColor)
                .conditionalLuminanceToAlpha(renderingMode != .fullColor)
            
            // 内容布局
            VStack(alignment: .trailing, spacing: 0) {
                Spacer()
                
                HStack {
                    Spacer()
                    
                    Text("♥ \(entry.value)ms · \(formatHRVTime(entry.date))")
                        .foregroundStyle(getTextColor(for: entry.value))
                        .font(.system(size: textFontSize, weight: .semibold))
                }
                .padding(.trailing, textOffsetX)
                .padding(.top, textOffsetY)
                
                ZStack {
                    // 进度条背景 - 根据HRV值选择对应的进度条图片
                    Image(getProgressImageName(for: entry.value))
                        .resizable()
                        .frame(width: progressWidth, height: progressHeight)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                    
                    // 计算进度点位置
                    let hrv = min(max(Double(entry.value), HRVManager.shared.minHRVValue), HRVManager.shared.maxHRVValue)
                    let normalizedValue = hrv / HRVManager.shared.maxHRVValue
                    let offsetX = CGFloat((normalizedValue * progressWidth) - (progressWidth / 2))
                    
                    // 进度点
                    Image("dot")
                        .resizable()
                        .frame(width: dotSize, height: dotSize)
                        .offset(x: offsetX, y: 0)
                        .widgetAccentable(renderingMode != .fullColor)
                        .conditionalLuminanceToAlpha(renderingMode != .fullColor)
                }
                .padding(.trailing, progressOffsetX)
                .padding(.bottom, progressBottomPadding)
                .padding(.top, progressOffsetY)
            }
        }
        .widgetURL(URL(string: "watchapp://hrv?value=\(entry.value)&time=\(formatTime(entry.date))"))
    }
    
    // 根据HRV值获取背景图片名称
    private func getBackgroundImageName(for hrvValue: Int) -> String {
        switch hrvValue {
        case ...19:
            return "1"  // 压力过载
        case 20...39:
            return "2"  // 注意压力
        case 40..<70:
            return "3"  // 悠然自在
        default:
            return "4"  // 活力满满
        }
    }
    
    // 根据HRV值获取进度条图片名称
    private func getProgressImageName(for hrvValue: Int) -> String {
        switch hrvValue {
        case ...19:
            return "process-1"  // 压力过载
        case 20...39:
            return "process-2"  // 注意压力
        case 40..<70:
            return "process-3"  // 悠然自在
        default:
            return "process-4"  // 活力满满
        }
    }
    
    // 格式化HRV数据的时间（显示在文字中的时间）
    private func formatHRVTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    // 根据HRV值获取文字颜色
    private func getTextColor(for hrvValue: Int) -> Color {
        switch hrvValue {
        case ...19:
            return Color(hex: "ffffff")  // 压力过载 - 白色
        case 20...39:
            return Color(hex: "ffffff")  // 注意压力 - 白色
        case 40..<70:
            return Color(hex: "a8eb54")  // 悠然自在 - 绿色
        default:
            return Color(hex: "ff61ab")  // 活力满满 - 粉色
        }
    }
}

@main
struct HRVLMYJXComplication: Widget {
    let kind: String = "HRVLMYJXComplication"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            if #available(watchOS 10.0, *) {
                HRVLMYJXComplicationEntryView(entry: entry)
                    .containerBackground(.fill.tertiary, for: .widget)
            } else {
                HRVLMYJXComplicationEntryView(entry: entry)
                    .padding()
                    .background()
            }
        }
        .configurationDisplayName("浪漫郁金香-HRV")
        .description("根据HRV的值显示不同状态的浪漫郁金香")
        .supportedFamilies([.accessoryRectangular])
    }
}

// 预览
struct HRVLMYJXComplication_Previews: PreviewProvider {
    static var previews: some View {
        let entry = HRVComplicationModel.preview
        let family: WidgetFamily = .accessoryRectangular
        if #available(watchOS 10.0, *) {
            HRVLMYJXComplicationEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
                .previewContext(WidgetPreviewContext(family: family))
        } else {
            HRVLMYJXComplicationEntryView(entry: entry)
                .padding()
                .background()
                .previewContext(WidgetPreviewContext(family: family))
        }
    }
}

// 扩展View以支持条件性luminanceToAlpha
extension View {
    @ViewBuilder
    func conditionalLuminanceToAlpha(_ condition: Bool) -> some View {
        if condition {
            self.luminanceToAlpha()
        } else {
            self
        }
    }
}
